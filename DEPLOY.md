# 🚀 部署到GitHub指南

## 📋 准备工作

1. **确保你有GitHub仓库的写入权限**
2. **配置Git认证**（选择其中一种方式）

### 方式1: 使用个人访问令牌（推荐）

1. 访问 GitHub Settings > Developer settings > Personal access tokens
2. 创建新的token，勾选 `repo` 权限
3. 复制生成的token
4. 在终端中运行：
   ```bash
   git remote set-url origin https://<EMAIL>/skyame4/augmentfree.git
   ```

### 方式2: 使用SSH密钥

1. 生成SSH密钥：
   ```bash
   ssh-keygen -t ed25519 -C "<EMAIL>"
   ```
2. 添加到GitHub账户
3. 使用SSH URL：
   ```bash
   git remote set-<NAME_EMAIL>:skyame4/augmentfree.git
   ```

## 📤 上传到GitHub

```bash
# 进入项目目录
cd /Users/<USER>/Desktop/AugmentCode-Free-master

# 推送到GitHub
git push -u origin main
```

## 🏗️ 触发自动构建

### 方式1: 创建Release标签（推荐）
```bash
# 创建并推送标签，这会触发自动构建和发布
git tag v1.0.0
git push origin v1.0.0
```

### 方式2: 手动触发
1. 访问 GitHub仓库页面
2. 点击 "Actions" 标签
3. 选择 "Build Desktop Applications" workflow
4. 点击 "Run workflow" 按钮

## 📦 构建产物

自动构建完成后，会生成以下文件：

### Windows
- `AugmentCode-Free-Windows-x64.zip`
  - 包含 `AugmentCode-Free-Windows-x64.exe`
  - 双击即可运行

### macOS Intel
- `AugmentCode-Free-macOS-Intel.tar.gz`
  - 包含 `AugmentCode-Free.app`
  - 双击即可运行
  - 支持 macOS 10.14+

### macOS Apple Silicon
- `AugmentCode-Free-macOS-AppleSilicon.tar.gz`
  - 包含 `AugmentCode-Free.app`
  - 双击即可运行
  - 支持 macOS 11.0+

## 🔧 GitHub Actions配置说明

我已经配置了完整的GitHub Actions工作流：

### 触发条件
- 推送到 `main` 分支
- 创建以 `v` 开头的标签
- 手动触发

### 构建特性
- **Windows**: 使用 `windows-latest` runner
- **macOS Intel**: 使用 `macos-13` runner (Intel芯片)
- **macOS Apple Silicon**: 使用 `macos-14` runner (Apple芯片)
- **自动打包**: 创建适合各平台的安装包
- **自动发布**: 标签推送时自动创建GitHub Release

### 文件结构
```
.github/
└── workflows/
    └── build.yml          # 主要的构建配置

build.sh                   # Linux/macOS本地构建脚本
build.bat                  # Windows本地构建脚本
build.spec                 # PyInstaller配置文件
BUILD.md                   # 详细构建说明
```

## ⚠️ 注意事项

1. **首次推送**: 确保仓库存在且有正确权限
2. **大文件**: 构建产物较大，GitHub有文件大小限制
3. **构建时间**: 完整构建需要10-15分钟
4. **依赖安装**: 确保requirements.txt包含所有依赖

## 🐛 常见问题

### 推送失败
```bash
# 检查远程仓库URL
git remote -v

# 重新设置远程仓库
git remote set-url origin https://github.com/skyame4/augmentfree.git
```

### 构建失败
1. 检查 GitHub Actions 日志
2. 确保所有依赖在 requirements.txt 中
3. 检查代码中是否有平台特定的路径问题

### 权限问题
- 确保GitHub token有足够权限
- 检查仓库设置中的Actions权限

## 📞 获取帮助

如果遇到问题：
1. 查看GitHub Actions的构建日志
2. 检查本文档的常见问题部分
3. 在GitHub仓库中创建Issue

---

**准备就绪后，运行以下命令完成部署：**

```bash
cd /Users/<USER>/Desktop/AugmentCode-Free-master
git push -u origin main
git tag v1.0.0
git push origin v1.0.0
```
