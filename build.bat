@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo 🚀 开始构建 AugmentCode-Free...

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python 未安装，请先安装Python
    pause
    exit /b 1
)

REM 检查pip
pip --version >nul 2>&1
if errorlevel 1 (
    echo ❌ pip 未安装，请先安装pip
    pause
    exit /b 1
)

REM 安装依赖
echo 📦 安装依赖...
pip install --upgrade pip
pip install -r requirements.txt
pip install pyinstaller

REM 清理之前的构建
echo 🧹 清理之前的构建...
if exist build rmdir /s /q build
if exist dist rmdir /s /q dist
if exist *.spec del *.spec

echo 🔧 检测到系统: Windows
echo 📱 构建平台: windows

REM 构建可执行文件
echo 🔨 开始构建...
pyinstaller --onefile ^
    --windowed ^
    --name "AugmentCode-Free-windows" ^
    --add-data "config;config" ^
    --add-data "core;core" ^
    --add-data "gui_qt6;gui_qt6" ^
    --add-data "locales;locales" ^
    --add-data "utils;utils" ^
    --hidden-import PyQt6 ^
    --hidden-import PyQt6.QtCore ^
    --hidden-import PyQt6.QtWidgets ^
    --hidden-import PyQt6.QtGui ^
    --hidden-import psutil ^
    --hidden-import click ^
    --hidden-import colorama ^
    main.py

REM 检查构建结果
if exist "dist\AugmentCode-Free-windows.exe" (
    echo ✅ 构建成功！
    echo 📁 可执行文件位置: dist\AugmentCode-Free-windows.exe
    
    REM 创建压缩包
    echo 📦 创建压缩包...
    cd dist
    if exist "C:\Program Files\7-Zip\7z.exe" (
        "C:\Program Files\7-Zip\7z.exe" a "AugmentCode-Free-windows.zip" "AugmentCode-Free-windows.exe"
        echo 📦 压缩包: dist\AugmentCode-Free-windows.zip
    ) else (
        echo ⚠️ 7-Zip 未安装，跳过压缩包创建
    )
    cd ..
    
    REM 显示文件信息
    echo 📊 文件信息:
    dir dist\
    
) else (
    echo ❌ 构建失败！
    pause
    exit /b 1
)

echo 🎉 构建完成！
pause
