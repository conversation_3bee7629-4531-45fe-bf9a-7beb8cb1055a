#!/usr/bin/env python3
"""
PyQt6主功能页面
替代原有的Tkinter主页面
"""

import psutil
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QComboBox,
    QTextEdit, QMessageBox, QSpacerItem, QSizePolicy, QFrame, QLabel, QPushButton
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QTextCursor

from .components import (
    TitleLabel, SubtitleLabel, SecondaryLabel, LinkLabel,
    ModernButton, StatusLabel, SectionFrame
)
from .workers import CloseIDEWorker, CleanDatabaseWorker, ModifyIDsWorker, RunAllWorker
from .font_manager import get_default_font, get_monospace_font
from augment_tools_core.common_utils import (
    IDEType, get_ide_display_name, get_ide_process_names
)
from language_manager import get_language_manager, get_text



class MainPage(QWidget):
    """PyQt6主功能页面"""
    
    def __init__(self, config_manager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.language_manager = get_language_manager(config_manager)
        
        # 当前工作线程
        self.current_worker = None
        
        # 进程检查缓存
        self._process_cache = {}
        self._cache_timer = QTimer()
        self._cache_timer.timeout.connect(self._clear_process_cache)
        self._cache_timer.start(2000)  # 每2秒清理缓存
        
        self._setup_ui()
        self._connect_signals()

        # 添加启动日志
        self._add_startup_logs()
    
    def _setup_ui(self):
        """设置紧凑的现代化用户界面"""
        # 主布局 - 减少边距
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(10, 10, 10, 10)
        main_layout.setSpacing(10)

        # 主内容区域 - 垂直布局，更紧凑
        content_widget = self._create_main_content()
        main_layout.addWidget(content_widget)

        # 状态显示
        self._create_status_section(main_layout)
    
    def _create_top_bar(self, parent_layout):
        """创建现代化顶部栏"""
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 16px;
                padding: 15px;
                margin: 5px;
            }
        """)
        top_layout = QHBoxLayout(top_frame)
        top_layout.setContentsMargins(20, 15, 20, 15)

        # 语言选择区域
        lang_container = QHBoxLayout()
        lang_label = SecondaryLabel(get_text("app.language"))
        lang_label.setStyleSheet("font-weight: 600; color: #64748b;")
        lang_container.addWidget(lang_label)

        self.language_combo = QComboBox()
        self.language_combo.setFont(get_default_font(12))
        self.language_combo.setMinimumWidth(150)

        # 填充语言选项
        available_langs = self.language_manager.get_available_languages()
        lang_values = list(available_langs.values())
        self.language_combo.addItems(lang_values)

        # 设置当前语言
        current_lang = self.language_manager.get_language()
        current_display = available_langs.get(current_lang, lang_values[0])
        current_index = lang_values.index(current_display) if current_display in lang_values else 0
        self.language_combo.setCurrentIndex(current_index)

        lang_container.addWidget(self.language_combo)
        lang_container.addStretch()

        top_layout.addLayout(lang_container)
        parent_layout.addWidget(top_frame)

    def _create_main_content(self):
        """创建紧凑的主内容区域"""
        main_frame = QFrame()
        main_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 15px;
                margin: 3px;
            }
        """)
        main_layout = QVBoxLayout(main_frame)
        main_layout.setContentsMargins(20, 15, 20, 15)
        main_layout.setSpacing(12)

        # 顶部：IDE选择区域
        ide_section = self._create_compact_ide_selection()
        main_layout.addWidget(ide_section)

        # 中间：按钮区域
        buttons_section = self._create_compact_buttons()
        main_layout.addWidget(buttons_section)

        # 底部：日志区域 - 紧凑版
        log_section = self._create_compact_log_section()
        main_layout.addWidget(log_section)

        return main_frame

    def _create_compact_ide_selection(self):
        """创建紧凑的IDE选择区域"""
        ide_frame = QFrame()
        ide_layout = QVBoxLayout(ide_frame)
        ide_layout.setContentsMargins(0, 0, 0, 0)
        ide_layout.setSpacing(8)

        # 标题
        ide_title = SubtitleLabel("选择 IDE:")
        ide_title.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 2px;
        """)
        ide_layout.addWidget(ide_title)

        # IDE选择下拉框
        self.ide_combo = QComboBox()
        self.ide_combo.setFont(get_default_font(12))
        self.ide_combo.addItems(["VS Code", "Cursor", "Windsurf", "JetBrains"])
        self.ide_combo.setMinimumHeight(38)
        self.ide_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #d1d5db;
                border-radius: 8px;
                padding: 10px 18px;
                font-size: 14px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f9fafb);
                color: #111827;
                font-weight: 500;
            }
            QComboBox:hover {
                border-color: #6366f1;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f3f4f6);
            }
            QComboBox:focus {
                border-color: #4f46e5;
                outline: none;
            }
            QComboBox::drop-down {
                border: none;
                width: 30px;
                background: transparent;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #6b7280;
                margin-right: 10px;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #d1d5db;
                border-radius: 6px;
                background-color: #ffffff;
                selection-background-color: #3b82f6;
                selection-color: white;
                padding: 3px;
            }
        """)

        # 设置默认选择
        last_ide = self.config_manager.get_last_selected_ide()
        if last_ide in ["VS Code", "Cursor", "Windsurf", "JetBrains"]:
            self.ide_combo.setCurrentText(last_ide)

        ide_layout.addWidget(self.ide_combo)
        return ide_frame

    def _create_compact_buttons(self):
        """创建紧凑的按钮区域"""
        buttons_frame = QFrame()
        buttons_layout = QVBoxLayout(buttons_frame)
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(10)

        # 主要操作按钮
        self.run_all_btn = ModernButton("🚀 一键修改所有配置", "primary")
        self.run_all_btn.setMinimumHeight(42)
        self.run_all_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6366f1, stop:1 #4f46e5);
                color: white;
                border: none;
                border-radius: 8px;
                padding: 10px 16px;
                font-weight: 600;
                font-size: 13px;
                min-height: 42px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5b21b6, stop:1 #7c3aed);
            }
            QPushButton:pressed {
                background: #4338ca;
            }
        """)
        buttons_layout.addWidget(self.run_all_btn)

        # 次要操作按钮 - 2x2网格
        grid_layout = QGridLayout()
        grid_layout.setSpacing(8)

        # 关闭IDE按钮
        self.close_ide_btn = ModernButton("⚠️ 关闭IDE", "warning")
        self.close_ide_btn.setMinimumHeight(35)
        self.close_ide_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b, stop:1 #d97706);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-weight: 600;
                font-size: 11px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d97706, stop:1 #b45309);
            }
        """)
        grid_layout.addWidget(self.close_ide_btn, 0, 0)

        # 清理数据库按钮
        self.clean_db_btn = ModernButton("🗑️ 清理数据库", "secondary")
        self.clean_db_btn.setMinimumHeight(35)
        self.clean_db_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6b7280, stop:1 #4b5563);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-weight: 600;
                font-size: 11px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4b5563, stop:1 #374151);
            }
        """)
        grid_layout.addWidget(self.clean_db_btn, 0, 1)

        # 修改遥测ID按钮
        self.modify_ids_btn = ModernButton("🔧 修改遥测ID", "secondary")
        self.modify_ids_btn.setMinimumHeight(35)
        self.modify_ids_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #10b981, stop:1 #059669);
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 12px;
                font-weight: 600;
                font-size: 11px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #059669, stop:1 #047857);
            }
        """)
        grid_layout.addWidget(self.modify_ids_btn, 1, 0, 1, 2)  # 跨两列

        buttons_layout.addLayout(grid_layout)
        return buttons_frame

    def _create_compact_log_section(self):
        """创建紧凑的日志区域"""
        log_frame = QFrame()
        log_layout = QVBoxLayout(log_frame)
        log_layout.setContentsMargins(8, 5, 8, 5)
        log_layout.setSpacing(3)

        # 超级紧凑的标题行
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(0)

        log_label = QLabel("日志")
        log_label.setStyleSheet("""
            font-size: 9px;
            color: #9ca3af;
            margin: 0;
            padding: 0;
            max-height: 12px;
        """)
        header_layout.addWidget(log_label)
        header_layout.addStretch()

        # 超小清理按钮
        self.clear_log_btn = QPushButton("×")
        self.clear_log_btn.setMaximumWidth(15)
        self.clear_log_btn.setMaximumHeight(12)
        self.clear_log_btn.setToolTip("清空")
        self.clear_log_btn.setStyleSheet("""
            QPushButton {
                background: none;
                color: #9ca3af;
                border: none;
                padding: 0;
                margin: 0;
                font-size: 10px;
                max-height: 12px;
            }
            QPushButton:hover {
                color: #6b7280;
            }
        """)
        header_layout.addWidget(self.clear_log_btn)

        log_layout.addLayout(header_layout)

        # 日志文本框 - 大幅增加高度
        self.log_text = QTextEdit()
        self.log_text.setFont(get_monospace_font(10))
        self.log_text.setReadOnly(True)
        self.log_text.setMinimumHeight(250)  # 适配600px高度窗口
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                padding: 8px;
                font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
                font-size: 10px;
                color: #374151;
                line-height: 1.4;
            }
        """)
        log_layout.addWidget(self.log_text)

        return log_frame

    def _add_startup_logs(self):
        """添加启动时的详细日志"""
        import datetime
        import platform
        import sys

        current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        startup_logs = [
            f"🚀 AugmentCode-Free 启动成功 - {current_time}",
            f"📱 系统信息: {platform.system()} {platform.release()}",
            f"🐍 Python版本: {sys.version.split()[0]}",
            f"🎯 当前选择的IDE: {self.ide_combo.currentText()}",
            "=" * 50,
            "📋 功能说明:",
            "  🚀 一键修改所有配置 - 执行完整的IDE配置修改流程",
            "  ⚠️ 关闭IDE - 强制关闭选定的IDE进程",
            "  🗑️ 清理数据库 - 清理IDE相关的数据库记录",
            "  🔧 修改遥测ID - 修改IDE的遥测标识符",
            "=" * 50,
            "✅ 系统就绪，请选择要执行的操作...",
            ""
        ]

        for log in startup_logs:
            self._add_log(log)


    
    def _create_main_card(self, parent_layout):
        """创建主要功能卡片"""
        main_card = QFrame()
        main_card.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 16px;
                padding: 20px;
                margin: 8px;
            }
        """)
        card_layout = QVBoxLayout(main_card)
        card_layout.setContentsMargins(30, 25, 30, 25)
        card_layout.setSpacing(25)

        # IDE选择区域
        ide_section = self._create_ide_selection_section()
        card_layout.addWidget(ide_section)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("background-color: #e2e8f0; height: 1px; border: none; margin: 10px 0;")
        card_layout.addWidget(separator)

        # 按钮区域
        buttons_section = self._create_buttons_grid()
        card_layout.addWidget(buttons_section)

        parent_layout.addWidget(main_card)

    def _create_left_section(self):
        """创建左侧主要功能区域"""
        left_frame = QFrame()
        left_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 15px;
                margin: 3px;
            }
        """)
        left_layout = QVBoxLayout(left_frame)
        left_layout.setContentsMargins(20, 15, 20, 15)
        left_layout.setSpacing(15)

        # IDE选择区域
        ide_section = self._create_ide_selection_section()
        left_layout.addWidget(ide_section)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("background-color: #e2e8f0; height: 1px; border: none; margin: 8px 0;")
        left_layout.addWidget(separator)

        # 按钮区域
        buttons_section = self._create_buttons_grid()
        left_layout.addWidget(buttons_section)

        # 底部添加少量弹性空间
        left_layout.addStretch(1)

        return left_frame

    def _create_right_section(self):
        """创建右侧日志区域"""
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                padding: 15px;
                margin: 3px;
            }
        """)
        right_layout = QVBoxLayout(right_frame)
        right_layout.setContentsMargins(15, 15, 15, 15)
        right_layout.setSpacing(10)

        # 日志标题和清理按钮
        header_layout = QHBoxLayout()
        log_label = SubtitleLabel("操作日志")
        log_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: #0f172a;
        """)
        header_layout.addWidget(log_label)
        header_layout.addStretch()

        # 清理日志按钮
        self.clear_log_btn = ModernButton("清空", "secondary")
        self.clear_log_btn.setMaximumWidth(80)
        self.clear_log_btn.setMinimumHeight(35)
        header_layout.addWidget(self.clear_log_btn)

        right_layout.addLayout(header_layout)

        # 创建日志文本框 - 占满右侧空间
        self.log_text = QTextEdit()
        self.log_text.setFont(get_monospace_font(11))
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 12px;
                color: #334155;
            }
        """)
        right_layout.addWidget(self.log_text)

        return right_frame

    def _create_ide_selection_section(self):
        """创建现代化IDE选择区域"""
        ide_frame = QFrame()
        ide_layout = QVBoxLayout(ide_frame)
        ide_layout.setSpacing(12)

        # IDE选择标签 - 简洁设计
        self.ide_label = SubtitleLabel("选择 IDE:")
        self.ide_label.setStyleSheet("""
            font-size: 15px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        """)
        ide_layout.addWidget(self.ide_label)

        # IDE下拉框 - 现代化设计
        self.ide_combo = QComboBox()
        self.ide_combo.setFont(get_default_font(13))
        self.ide_combo.addItems(["VS Code", "Cursor", "Windsurf", "JetBrains"])
        self.ide_combo.setMinimumHeight(50)
        self.ide_combo.setStyleSheet("""
            QComboBox {
                border: 2px solid #d1d5db;
                border-radius: 10px;
                padding: 12px 20px;
                font-size: 14px;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f9fafb);
                color: #111827;
                font-weight: 500;
                selection-background-color: #3b82f6;
            }
            QComboBox:hover {
                border-color: #6366f1;
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f3f4f6);
            }
            QComboBox:focus {
                border-color: #4f46e5;
                outline: none;
                box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
            }
            QComboBox::drop-down {
                border: none;
                width: 35px;
                background: transparent;
            }
            QComboBox::down-arrow {
                image: none;
                border-left: 6px solid transparent;
                border-right: 6px solid transparent;
                border-top: 6px solid #6b7280;
                margin-right: 12px;
            }
            QComboBox::down-arrow:hover {
                border-top-color: #4f46e5;
            }
            QComboBox QAbstractItemView {
                border: 2px solid #d1d5db;
                border-radius: 8px;
                background-color: #ffffff;
                selection-background-color: #3b82f6;
                selection-color: white;
                padding: 5px;
            }
        """)

        # 设置默认选择
        last_ide = self.config_manager.get_last_selected_ide()
        if last_ide in ["VS Code", "Cursor", "Windsurf", "JetBrains"]:
            self.ide_combo.setCurrentText(last_ide)

        ide_layout.addWidget(self.ide_combo)

        return ide_frame
    
    def _create_buttons_grid(self):
        """创建现代化按钮网格"""
        buttons_frame = QFrame()
        grid_layout = QVBoxLayout(buttons_frame)
        grid_layout.setSpacing(12)

        # 主要操作按钮（一键修改）- 重新设计
        self.run_all_btn = ModernButton("🚀 一键修改所有配置", "primary")
        self.run_all_btn.setMinimumHeight(55)
        self.run_all_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6366f1, stop:1 #4f46e5);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 15px 25px;
                font-weight: 600;
                font-size: 15px;
                min-height: 55px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #5b21b6, stop:1 #7c3aed);
                transform: translateY(-1px);
            }
            QPushButton:pressed {
                background: #4338ca;
                transform: translateY(0px);
            }
        """)
        grid_layout.addWidget(self.run_all_btn)

        # 次要操作按钮行
        secondary_layout = QHBoxLayout()
        secondary_layout.setSpacing(12)

        # 关闭IDE按钮 - 重新设计
        self.close_ide_btn = ModernButton("⚠️ 关闭IDE", "warning")
        self.close_ide_btn.setMinimumHeight(45)
        self.close_ide_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #f59e0b, stop:1 #d97706);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-weight: 600;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #d97706, stop:1 #b45309);
            }
            QPushButton:pressed {
                background: #92400e;
            }
        """)
        secondary_layout.addWidget(self.close_ide_btn)

        # 清理数据库按钮 - 重新设计
        self.clean_db_btn = ModernButton("🗑️ 清理数据库", "secondary")
        self.clean_db_btn.setMinimumHeight(45)
        self.clean_db_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #6b7280, stop:1 #4b5563);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-weight: 600;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4b5563, stop:1 #374151);
            }
            QPushButton:pressed {
                background: #1f2937;
            }
        """)
        secondary_layout.addWidget(self.clean_db_btn)

        grid_layout.addLayout(secondary_layout)

        # 修改遥测ID按钮 - 重新设计
        self.modify_ids_btn = ModernButton("🔧 修改遥测ID", "secondary")
        self.modify_ids_btn.setMinimumHeight(45)
        self.modify_ids_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #10b981, stop:1 #059669);
                color: white;
                border: none;
                border-radius: 10px;
                padding: 12px 20px;
                font-weight: 600;
                font-size: 13px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #059669, stop:1 #047857);
            }
            QPushButton:pressed {
                background: #065f46;
            }
        """)
        grid_layout.addWidget(self.modify_ids_btn)

        return buttons_frame
    
    def _create_status_section(self, parent_layout):
        """创建状态显示区域"""
        self.status_label = StatusLabel()
        parent_layout.addWidget(self.status_label)
    

    
    def _connect_signals(self):
        """连接信号"""
        # IDE选择
        self.ide_combo.currentTextChanged.connect(self._on_ide_change)

        # 按钮点击
        self.run_all_btn.clicked.connect(self._run_all_clicked)
        self.close_ide_btn.clicked.connect(self._close_ide_clicked)
        self.clean_db_btn.clicked.connect(self._clean_database_clicked)
        self.modify_ids_btn.clicked.connect(self._modify_ids_clicked)
        self.clear_log_btn.clicked.connect(self._clear_log)
        


    def _clear_process_cache(self):
        """清理进程缓存"""
        self._process_cache.clear()

    def _on_language_change(self, selected_display: str):
        """处理语言变更"""
        available_langs = self.language_manager.get_available_languages()

        for code, display in available_langs.items():
            if display == selected_display:
                self.language_manager.set_language(code)
                self._update_ui_texts()
                break

    def _on_ide_change(self, selected_ide: str):
        """处理IDE选择变更"""
        self.config_manager.set_last_selected_ide(selected_ide)

    def _update_ui_texts(self):
        """更新所有UI文本"""
        # 更新按钮文本
        self.run_all_btn.setText(get_text("buttons.run_all"))
        self.close_ide_btn.setText(get_text("buttons.close_ide"))
        self.clean_db_btn.setText(get_text("buttons.clean_db"))
        self.modify_ids_btn.setText(get_text("buttons.modify_ids"))
        self.clear_log_btn.setText(get_text("buttons.clear_log"))
        # 根据语言调整按钮宽度
        if self.config_manager.get_language() == "en_US":
            # 清理日志按钮现在在日志框内，需要重新设置大小
            btn_width = 120
            self.clear_log_btn.setFixedSize(btn_width, 25)
        else:
            # 清理日志按钮现在在日志框内，需要重新设置大小
            btn_width = 100
            self.clear_log_btn.setFixedSize(btn_width, 25)

        # 更新标签文本
        self.title_label.setText(get_text("app.title"))
        self.welcome_label.setText("多IDE维护工具 - 现代化界面")
        self.ide_label.setText(get_text("app.select_ide"))





    def _clear_log(self):
        """清空日志"""
        self.log_text.clear()

    def _add_log(self, message: str):
        """添加日志信息"""
        self.log_text.append(message)
        # 滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def get_selected_ide_type(self) -> IDEType:
        """获取选中的IDE类型"""
        ide_name = self.ide_combo.currentText()
        if ide_name == "VS Code":
            return IDEType.VSCODE
        elif ide_name == "Cursor":
            return IDEType.CURSOR
        elif ide_name == "Windsurf":
            return IDEType.WINDSURF
        elif ide_name == "JetBrains":
            return IDEType.JETBRAINS
        else:
            return IDEType.VSCODE  # 默认

    def _is_ide_running(self, ide_type: IDEType) -> bool:
        """检查IDE是否正在运行"""
        cache_key = f"ide_running_{ide_type.value}"

        # 检查缓存
        if cache_key in self._process_cache:
            return self._process_cache[cache_key]

        try:
            process_names = get_ide_process_names(ide_type)
            is_running = False

            for proc in psutil.process_iter(['name']):
                if proc.info['name'] in process_names:
                    is_running = True
                    break

            # 缓存结果
            self._process_cache[cache_key] = is_running
            return is_running

        except Exception:
            self._process_cache[cache_key] = False
            return False

    def _set_buttons_enabled(self, enabled: bool):
        """设置所有按钮的启用状态"""
        self.run_all_btn.set_enabled_state(enabled)
        self.close_ide_btn.set_enabled_state(enabled)
        self.clean_db_btn.set_enabled_state(enabled)
        self.modify_ids_btn.set_enabled_state(enabled)

    def _show_warning_dialog(self, title: str, message: str) -> bool:
        """显示警告对话框"""
        reply = QMessageBox.question(
            self, title, message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        return reply == QMessageBox.StandardButton.Yes

    def _show_info_dialog(self, title: str, message: str):
        """显示信息对话框"""
        QMessageBox.information(self, title, message)

    def _run_all_clicked(self):
        """处理一键修改按钮点击"""
        ide_type = self.get_selected_ide_type()
        ide_name = get_ide_display_name(ide_type)

        import datetime
        current_time = datetime.datetime.now().strftime("%H:%M:%S")

        self._add_log(f"🚀 [{current_time}] 用户点击了'一键修改所有配置'按钮")
        self._add_log(f"🎯 目标IDE: {ide_name} ({ide_type})")
        self._add_log(f"🔍 准备执行完整配置修改流程...")

        # 显示确认对话框
        self._add_log("⚠️ 显示用户确认对话框...")
        if not self._show_warning_dialog(
            get_text("dialogs.titles.run_all_confirm"),
            get_text("dialogs.messages.run_all_warning", ide_name=ide_name)
        ):
            self._add_log("❌ 用户取消了操作")
            return

        self._add_log("✅ 用户确认执行操作")
        self._add_log("🔒 禁用所有按钮，开始执行...")

        # 禁用按钮
        self._set_buttons_enabled(False)

        # 创建并启动工作线程
        self._add_log("🧵 创建工作线程...")
        self.current_worker = RunAllWorker(ide_type, "augment")
        self._connect_worker_signals(self.current_worker)
        self._add_log("▶️ 启动后台任务执行...")
        self.current_worker.start()

    def _close_ide_clicked(self):
        """处理关闭IDE按钮点击"""
        ide_type = self.get_selected_ide_type()
        ide_name = get_ide_display_name(ide_type)

        import datetime
        current_time = datetime.datetime.now().strftime("%H:%M:%S")

        self._add_log(f"⚠️ [{current_time}] 用户点击了'关闭IDE'按钮")
        self._add_log(f"🎯 目标IDE: {ide_name} ({ide_type})")
        self._add_log("🔍 检查IDE进程状态...")

        # 显示确认对话框
        self._add_log("⚠️ 显示关闭确认对话框...")
        if not self._show_warning_dialog(
            get_text("dialogs.titles.close_confirm", ide_name=ide_name),
            get_text("dialogs.messages.close_warning", ide_name=ide_name)
        ):
            self._add_log("❌ 用户取消了关闭操作")
            return

        self._add_log("✅ 用户确认关闭IDE")
        self._add_log("🔒 禁用按钮，开始关闭进程...")

        # 禁用按钮
        self._set_buttons_enabled(False)

        # 创建并启动工作线程
        self._add_log("🧵 创建IDE关闭工作线程...")
        self.current_worker = CloseIDEWorker(ide_type)
        self._connect_worker_signals(self.current_worker)
        self._add_log("▶️ 开始强制关闭IDE进程...")
        self.current_worker.start()

    def _clean_database_clicked(self):
        """处理清理数据库按钮点击"""
        ide_type = self.get_selected_ide_type()
        ide_name = get_ide_display_name(ide_type)

        # JetBrains 产品不需要数据库清理，引导用户使用修改遥测ID功能
        if ide_type == IDEType.JETBRAINS:
            self._show_info_dialog(
                get_text("dialogs.titles.jetbrains_notice"),
                get_text("dialogs.messages.jetbrains_db_notice", ide_name=ide_name)
            )
            return

        # 检查IDE是否正在运行
        if self._is_ide_running(ide_type):
            self._show_info_dialog(
                get_text("dialogs.titles.ide_running", ide_name=ide_name),
                get_text("dialogs.messages.ide_running_warning", ide_name=ide_name)
            )
            return

        # 禁用按钮
        self._set_buttons_enabled(False)

        # 创建并启动工作线程
        self.current_worker = CleanDatabaseWorker(ide_type, "augment")
        self._connect_worker_signals(self.current_worker)
        self.current_worker.start()

    def _modify_ids_clicked(self):
        """处理修改遥测ID按钮点击"""
        ide_type = self.get_selected_ide_type()
        ide_name = get_ide_display_name(ide_type)

        # 检查IDE是否正在运行
        if self._is_ide_running(ide_type):
            self._show_info_dialog(
                get_text("dialogs.titles.ide_running", ide_name=ide_name),
                get_text("dialogs.messages.ide_running_warning", ide_name=ide_name)
            )
            return

        # 禁用按钮
        self._set_buttons_enabled(False)

        # 创建并启动工作线程
        self.current_worker = ModifyIDsWorker(ide_type)
        self._connect_worker_signals(self.current_worker)
        self.current_worker.start()

    def _connect_worker_signals(self, worker):
        """连接工作线程信号"""
        worker.progress_updated.connect(self._add_log)
        worker.status_changed.connect(self.status_label.show_status)
        worker.task_completed.connect(self._on_task_completed)

    def _on_task_completed(self, success: bool):
        """处理任务完成"""
        # 重新启用按钮
        self._set_buttons_enabled(True)

        # 清理工作线程
        if self.current_worker:
            self.current_worker.deleteLater()
            self.current_worker = None

        # 显示完成状态
        if success:
            self.status_label.show_status(get_text("status.success"), "success")
        else:
            self.status_label.show_status(get_text("status.error"), "error")

        # 3秒后隐藏状态
        QTimer.singleShot(3000, self.status_label.hide_status)
