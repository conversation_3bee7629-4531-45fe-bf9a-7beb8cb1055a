#!/usr/bin/env python3
"""
PyQt6主功能页面
替代原有的Tkinter主页面
"""

import psutil
from PyQt6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QComboBox,
    QTextEdit, QMessageBox, QSpacerItem, QSizePolicy, QFrame
)
from PyQt6.QtCore import Qt, pyqtSignal, QTimer
from PyQt6.QtGui import QTextCursor

from .components import (
    TitleLabel, SubtitleLabel, SecondaryLabel, LinkLabel,
    ModernButton, StatusLabel, SectionFrame
)
from .workers import CloseIDEWorker, CleanDatabaseWorker, ModifyIDsWorker, RunAllWorker
from .font_manager import get_default_font, get_monospace_font
from augment_tools_core.common_utils import (
    IDEType, get_ide_display_name, get_ide_process_names
)
from language_manager import get_language_manager, get_text



class MainPage(QWidget):
    """PyQt6主功能页面"""
    
    def __init__(self, config_manager, parent=None):
        super().__init__(parent)
        self.config_manager = config_manager
        self.language_manager = get_language_manager(config_manager)
        
        # 当前工作线程
        self.current_worker = None
        
        # 进程检查缓存
        self._process_cache = {}
        self._cache_timer = QTimer()
        self._cache_timer.timeout.connect(self._clear_process_cache)
        self._cache_timer.start(2000)  # 每2秒清理缓存
        
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置现代化用户界面 - 左右布局"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 15, 20, 15)
        main_layout.setSpacing(15)

        # 顶部栏（语言选择）
        self._create_top_bar(main_layout)

        # 主标题
        self._create_title_section(main_layout)

        # 左右分栏布局
        content_layout = QHBoxLayout()
        content_layout.setSpacing(20)

        # 左侧：主要功能区域
        left_section = self._create_left_section()
        content_layout.addWidget(left_section, 1)  # 占比1

        # 右侧：日志区域
        right_section = self._create_right_section()
        content_layout.addWidget(right_section, 1)  # 占比1

        main_layout.addLayout(content_layout)

        # 状态显示
        self._create_status_section(main_layout)

        # 添加弹性空间
        main_layout.addStretch()
    
    def _create_top_bar(self, parent_layout):
        """创建现代化顶部栏"""
        top_frame = QFrame()
        top_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 16px;
                padding: 15px;
                margin: 5px;
            }
        """)
        top_layout = QHBoxLayout(top_frame)
        top_layout.setContentsMargins(20, 15, 20, 15)

        # 语言选择区域
        lang_container = QHBoxLayout()
        lang_label = SecondaryLabel(get_text("app.language"))
        lang_label.setStyleSheet("font-weight: 600; color: #64748b;")
        lang_container.addWidget(lang_label)

        self.language_combo = QComboBox()
        self.language_combo.setFont(get_default_font(12))
        self.language_combo.setMinimumWidth(150)

        # 填充语言选项
        available_langs = self.language_manager.get_available_languages()
        lang_values = list(available_langs.values())
        self.language_combo.addItems(lang_values)

        # 设置当前语言
        current_lang = self.language_manager.get_language()
        current_display = available_langs.get(current_lang, lang_values[0])
        current_index = lang_values.index(current_display) if current_display in lang_values else 0
        self.language_combo.setCurrentIndex(current_index)

        lang_container.addWidget(self.language_combo)
        lang_container.addStretch()

        top_layout.addLayout(lang_container)
        parent_layout.addWidget(top_frame)
    
    def _create_title_section(self, parent_layout):
        """创建现代化标题区域"""
        title_frame = QFrame()
        title_layout = QVBoxLayout(title_frame)
        title_layout.setContentsMargins(0, 20, 0, 20)
        title_layout.setSpacing(10)

        # 主标题
        self.title_label = TitleLabel(get_text("app.title"), 32)
        self.title_label.setStyleSheet("""
            font-size: 32px;
            font-weight: 700;
            color: #0f172a;
            margin: 10px 0;
        """)
        title_layout.addWidget(self.title_label)

        # 副标题
        self.welcome_label = SecondaryLabel("多IDE维护工具 - 现代化界面")
        self.welcome_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.welcome_label.setStyleSheet("""
            font-size: 16px;
            color: #64748b;
            font-weight: 500;
        """)
        title_layout.addWidget(self.welcome_label)

        parent_layout.addWidget(title_frame)
    
    def _create_main_card(self, parent_layout):
        """创建主要功能卡片"""
        main_card = QFrame()
        main_card.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 16px;
                padding: 20px;
                margin: 8px;
            }
        """)
        card_layout = QVBoxLayout(main_card)
        card_layout.setContentsMargins(30, 25, 30, 25)
        card_layout.setSpacing(25)

        # IDE选择区域
        ide_section = self._create_ide_selection_section()
        card_layout.addWidget(ide_section)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("background-color: #e2e8f0; height: 1px; border: none; margin: 10px 0;")
        card_layout.addWidget(separator)

        # 按钮区域
        buttons_section = self._create_buttons_grid()
        card_layout.addWidget(buttons_section)

        parent_layout.addWidget(main_card)

    def _create_left_section(self):
        """创建左侧主要功能区域"""
        left_frame = QFrame()
        left_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 16px;
                padding: 20px;
                margin: 5px;
            }
        """)
        left_layout = QVBoxLayout(left_frame)
        left_layout.setContentsMargins(25, 25, 25, 25)
        left_layout.setSpacing(25)

        # IDE选择区域
        ide_section = self._create_ide_selection_section()
        left_layout.addWidget(ide_section)

        # 分隔线
        separator = QFrame()
        separator.setFrameShape(QFrame.Shape.HLine)
        separator.setStyleSheet("background-color: #e2e8f0; height: 1px; border: none; margin: 10px 0;")
        left_layout.addWidget(separator)

        # 按钮区域
        buttons_section = self._create_buttons_grid()
        left_layout.addWidget(buttons_section)

        # 添加弹性空间
        left_layout.addStretch()

        return left_frame

    def _create_right_section(self):
        """创建右侧日志区域"""
        right_frame = QFrame()
        right_frame.setStyleSheet("""
            QFrame {
                background-color: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 16px;
                padding: 20px;
                margin: 5px;
            }
        """)
        right_layout = QVBoxLayout(right_frame)
        right_layout.setContentsMargins(20, 20, 20, 20)
        right_layout.setSpacing(15)

        # 日志标题和清理按钮
        header_layout = QHBoxLayout()
        log_label = SubtitleLabel("操作日志")
        log_label.setStyleSheet("""
            font-size: 16px;
            font-weight: 600;
            color: #0f172a;
        """)
        header_layout.addWidget(log_label)
        header_layout.addStretch()

        # 清理日志按钮
        self.clear_log_btn = ModernButton("清空", "secondary")
        self.clear_log_btn.setMaximumWidth(80)
        self.clear_log_btn.setMinimumHeight(35)
        header_layout.addWidget(self.clear_log_btn)

        right_layout.addLayout(header_layout)

        # 创建日志文本框 - 占满右侧空间
        self.log_text = QTextEdit()
        self.log_text.setFont(get_monospace_font(11))
        self.log_text.setReadOnly(True)
        self.log_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 12px;
                color: #334155;
            }
        """)
        right_layout.addWidget(self.log_text)

        return right_frame

    def _create_ide_selection_section(self):
        """创建IDE选择区域"""
        ide_frame = QFrame()
        ide_layout = QVBoxLayout(ide_frame)
        ide_layout.setSpacing(15)

        # IDE选择标签
        self.ide_label = SubtitleLabel(get_text("app.select_ide"))
        self.ide_label.setStyleSheet("""
            font-size: 18px;
            font-weight: 600;
            color: #0f172a;
            margin-bottom: 5px;
        """)
        ide_layout.addWidget(self.ide_label)

        # IDE下拉框
        self.ide_combo = QComboBox()
        self.ide_combo.setFont(get_default_font(14))
        self.ide_combo.addItems(["VS Code", "Cursor", "Windsurf", "JetBrains"])
        self.ide_combo.setMinimumHeight(50)

        # 设置默认选择
        last_ide = self.config_manager.get_last_selected_ide()
        if last_ide in ["VS Code", "Cursor", "Windsurf", "JetBrains"]:
            self.ide_combo.setCurrentText(last_ide)

        ide_layout.addWidget(self.ide_combo)

        return ide_frame
    
    def _create_buttons_grid(self):
        """创建现代化按钮网格"""
        buttons_frame = QFrame()
        grid_layout = QVBoxLayout(buttons_frame)
        grid_layout.setSpacing(15)

        # 主要操作按钮（一键修改）
        self.run_all_btn = ModernButton(get_text("buttons.run_all"), "primary")
        self.run_all_btn.setMinimumHeight(60)
        self.run_all_btn.setStyleSheet("""
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #3b82f6,
                                           stop:1 #2563eb);
                color: white;
                border: none;
                border-radius: 12px;
                padding: 16px 32px;
                font-weight: 700;
                font-size: 16px;
                min-height: 60px;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                           stop:0 #2563eb,
                                           stop:1 #3b82f6);
            }
            QPushButton:pressed {
                background: #2563eb;
            }
        """)
        grid_layout.addWidget(self.run_all_btn)

        # 次要操作按钮行
        secondary_layout = QHBoxLayout()
        secondary_layout.setSpacing(15)

        # 关闭IDE按钮
        self.close_ide_btn = ModernButton(get_text("buttons.close_ide"), "warning")
        self.close_ide_btn.setMinimumHeight(45)
        secondary_layout.addWidget(self.close_ide_btn)

        # 清理数据库按钮
        self.clean_db_btn = ModernButton(get_text("buttons.clean_db"), "secondary")
        self.clean_db_btn.setMinimumHeight(45)
        secondary_layout.addWidget(self.clean_db_btn)

        grid_layout.addLayout(secondary_layout)

        # 修改遥测ID按钮
        self.modify_ids_btn = ModernButton(get_text("buttons.modify_ids"), "secondary")
        self.modify_ids_btn.setMinimumHeight(45)
        grid_layout.addWidget(self.modify_ids_btn)

        return buttons_frame
    
    def _create_status_section(self, parent_layout):
        """创建状态显示区域"""
        self.status_label = StatusLabel()
        parent_layout.addWidget(self.status_label)
    

    
    def _connect_signals(self):
        """连接信号"""
        # 语言切换
        self.language_combo.currentTextChanged.connect(self._on_language_change)
        
        # IDE选择
        self.ide_combo.currentTextChanged.connect(self._on_ide_change)
        
        # 按钮点击
        self.run_all_btn.clicked.connect(self._run_all_clicked)
        self.close_ide_btn.clicked.connect(self._close_ide_clicked)
        self.clean_db_btn.clicked.connect(self._clean_database_clicked)
        self.modify_ids_btn.clicked.connect(self._modify_ids_clicked)
        self.clear_log_btn.clicked.connect(self._clear_log)
        


    def _clear_process_cache(self):
        """清理进程缓存"""
        self._process_cache.clear()

    def _on_language_change(self, selected_display: str):
        """处理语言变更"""
        available_langs = self.language_manager.get_available_languages()

        for code, display in available_langs.items():
            if display == selected_display:
                self.language_manager.set_language(code)
                self._update_ui_texts()
                break

    def _on_ide_change(self, selected_ide: str):
        """处理IDE选择变更"""
        self.config_manager.set_last_selected_ide(selected_ide)

    def _update_ui_texts(self):
        """更新所有UI文本"""
        # 更新按钮文本
        self.run_all_btn.setText(get_text("buttons.run_all"))
        self.close_ide_btn.setText(get_text("buttons.close_ide"))
        self.clean_db_btn.setText(get_text("buttons.clean_db"))
        self.modify_ids_btn.setText(get_text("buttons.modify_ids"))
        self.clear_log_btn.setText(get_text("buttons.clear_log"))
        # 根据语言调整按钮宽度
        if self.config_manager.get_language() == "en_US":
            # 清理日志按钮现在在日志框内，需要重新设置大小
            btn_width = 120
            self.clear_log_btn.setFixedSize(btn_width, 25)
        else:
            # 清理日志按钮现在在日志框内，需要重新设置大小
            btn_width = 100
            self.clear_log_btn.setFixedSize(btn_width, 25)

        # 更新标签文本
        self.title_label.setText(get_text("app.title"))
        self.welcome_label.setText("多IDE维护工具 - 现代化界面")
        self.ide_label.setText(get_text("app.select_ide"))





    def _clear_log(self):
        """清空日志"""
        self.log_text.clear()

    def _add_log(self, message: str):
        """添加日志信息"""
        self.log_text.append(message)
        # 滚动到底部
        cursor = self.log_text.textCursor()
        cursor.movePosition(QTextCursor.MoveOperation.End)
        self.log_text.setTextCursor(cursor)

    def get_selected_ide_type(self) -> IDEType:
        """获取选中的IDE类型"""
        ide_name = self.ide_combo.currentText()
        if ide_name == "VS Code":
            return IDEType.VSCODE
        elif ide_name == "Cursor":
            return IDEType.CURSOR
        elif ide_name == "Windsurf":
            return IDEType.WINDSURF
        elif ide_name == "JetBrains":
            return IDEType.JETBRAINS
        else:
            return IDEType.VSCODE  # 默认

    def _is_ide_running(self, ide_type: IDEType) -> bool:
        """检查IDE是否正在运行"""
        cache_key = f"ide_running_{ide_type.value}"

        # 检查缓存
        if cache_key in self._process_cache:
            return self._process_cache[cache_key]

        try:
            process_names = get_ide_process_names(ide_type)
            is_running = False

            for proc in psutil.process_iter(['name']):
                if proc.info['name'] in process_names:
                    is_running = True
                    break

            # 缓存结果
            self._process_cache[cache_key] = is_running
            return is_running

        except Exception:
            self._process_cache[cache_key] = False
            return False

    def _set_buttons_enabled(self, enabled: bool):
        """设置所有按钮的启用状态"""
        self.run_all_btn.set_enabled_state(enabled)
        self.close_ide_btn.set_enabled_state(enabled)
        self.clean_db_btn.set_enabled_state(enabled)
        self.modify_ids_btn.set_enabled_state(enabled)

    def _show_warning_dialog(self, title: str, message: str) -> bool:
        """显示警告对话框"""
        reply = QMessageBox.question(
            self, title, message,
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )
        return reply == QMessageBox.StandardButton.Yes

    def _show_info_dialog(self, title: str, message: str):
        """显示信息对话框"""
        QMessageBox.information(self, title, message)

    def _run_all_clicked(self):
        """处理一键修改按钮点击"""
        ide_type = self.get_selected_ide_type()
        ide_name = get_ide_display_name(ide_type)

        # 显示确认对话框
        if not self._show_warning_dialog(
            get_text("dialogs.titles.run_all_confirm"),
            get_text("dialogs.messages.run_all_warning", ide_name=ide_name)
        ):
            return

        # 禁用按钮
        self._set_buttons_enabled(False)

        # 创建并启动工作线程
        self.current_worker = RunAllWorker(ide_type, "augment")
        self._connect_worker_signals(self.current_worker)
        self.current_worker.start()

    def _close_ide_clicked(self):
        """处理关闭IDE按钮点击"""
        ide_type = self.get_selected_ide_type()
        ide_name = get_ide_display_name(ide_type)

        # 显示确认对话框
        if not self._show_warning_dialog(
            get_text("dialogs.titles.close_confirm", ide_name=ide_name),
            get_text("dialogs.messages.close_warning", ide_name=ide_name)
        ):
            return

        # 禁用按钮
        self._set_buttons_enabled(False)

        # 创建并启动工作线程
        self.current_worker = CloseIDEWorker(ide_type)
        self._connect_worker_signals(self.current_worker)
        self.current_worker.start()

    def _clean_database_clicked(self):
        """处理清理数据库按钮点击"""
        ide_type = self.get_selected_ide_type()
        ide_name = get_ide_display_name(ide_type)

        # JetBrains 产品不需要数据库清理，引导用户使用修改遥测ID功能
        if ide_type == IDEType.JETBRAINS:
            self._show_info_dialog(
                get_text("dialogs.titles.jetbrains_notice"),
                get_text("dialogs.messages.jetbrains_db_notice", ide_name=ide_name)
            )
            return

        # 检查IDE是否正在运行
        if self._is_ide_running(ide_type):
            self._show_info_dialog(
                get_text("dialogs.titles.ide_running", ide_name=ide_name),
                get_text("dialogs.messages.ide_running_warning", ide_name=ide_name)
            )
            return

        # 禁用按钮
        self._set_buttons_enabled(False)

        # 创建并启动工作线程
        self.current_worker = CleanDatabaseWorker(ide_type, "augment")
        self._connect_worker_signals(self.current_worker)
        self.current_worker.start()

    def _modify_ids_clicked(self):
        """处理修改遥测ID按钮点击"""
        ide_type = self.get_selected_ide_type()
        ide_name = get_ide_display_name(ide_type)

        # 检查IDE是否正在运行
        if self._is_ide_running(ide_type):
            self._show_info_dialog(
                get_text("dialogs.titles.ide_running", ide_name=ide_name),
                get_text("dialogs.messages.ide_running_warning", ide_name=ide_name)
            )
            return

        # 禁用按钮
        self._set_buttons_enabled(False)

        # 创建并启动工作线程
        self.current_worker = ModifyIDsWorker(ide_type)
        self._connect_worker_signals(self.current_worker)
        self.current_worker.start()

    def _connect_worker_signals(self, worker):
        """连接工作线程信号"""
        worker.progress_updated.connect(self._add_log)
        worker.status_changed.connect(self.status_label.show_status)
        worker.task_completed.connect(self._on_task_completed)

    def _on_task_completed(self, success: bool):
        """处理任务完成"""
        # 重新启用按钮
        self._set_buttons_enabled(True)

        # 清理工作线程
        if self.current_worker:
            self.current_worker.deleteLater()
            self.current_worker = None

        # 显示完成状态
        if success:
            self.status_label.show_status(get_text("status.success"), "success")
        else:
            self.status_label.show_status(get_text("status.error"), "error")

        # 3秒后隐藏状态
        QTimer.singleShot(3000, self.status_label.hide_status)
