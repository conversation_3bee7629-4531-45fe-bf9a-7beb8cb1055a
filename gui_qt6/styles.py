#!/usr/bin/env python3
"""
CursorPro风格样式表
现代化的UI样式定义
"""

# 现代化颜色系统 - 深色主题风格
COLORS = {
    'primary': '#3b82f6',       # 现代蓝色主色调
    'primary_hover': '#2563eb', # 主色调悬停
    'secondary': '#64748b',     # 现代灰色
    'secondary_hover': '#475569', # 次要颜色悬停
    'warning': '#ef4444',       # 现代红色
    'warning_hover': '#dc2626', # 警告色悬停
    'success': '#10b981',       # 现代绿色
    'background': '#f8fafc',    # 浅灰背景
    'surface': '#ffffff',       # 纯白表面
    'card': '#f1f5f9',          # 卡片背景
    'text_primary': '#0f172a',  # 深色文字
    'text_secondary': '#64748b', # 中等灰色文字
    'text_disabled': '#94a3b8', # 禁用文字
    'border': '#e2e8f0',        # 浅边框
    'border_hover': '#cbd5e1',  # 悬停边框
    'warning_bg': '#fef2f2',    # 警告背景
    'warning_text': '#991b1b',  # 警告文字
    'accent': '#8b5cf6',        # 紫色强调色
    'gradient_start': '#3b82f6', # 渐变开始
    'gradient_end': '#8b5cf6'   # 渐变结束
}


def get_main_window_style() -> str:
    """主窗口样式"""
    return f"""
    QMainWindow {{
        background-color: {COLORS['background']};
        color: {COLORS['text_primary']};
    }}
    
    QWidget {{
        background-color: {COLORS['background']};
        color: {COLORS['text_primary']};
    }}
    """


def get_button_style() -> str:
    """现代化按钮样式"""
    return f"""
    QPushButton {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['primary']},
                                   stop:1 {COLORS['primary_hover']});
        color: white;
        border: none;
        border-radius: 12px;
        padding: 14px 28px;
        font-weight: 600;
        font-size: 14px;
        min-height: 24px;
    }}

    QPushButton:hover {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['primary_hover']},
                                   stop:1 {COLORS['primary']});
    }}

    QPushButton:pressed {{
        background: {COLORS['primary_hover']};
    }}

    QPushButton:disabled {{
        background-color: {COLORS['text_disabled']};
        color: {COLORS['text_secondary']};
    }}

    QPushButton.secondary {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['secondary']},
                                   stop:1 {COLORS['secondary_hover']});
        color: white;
    }}

    QPushButton.secondary:hover {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['secondary_hover']},
                                   stop:1 {COLORS['secondary']});
    }}

    QPushButton.warning {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['warning']},
                                   stop:1 {COLORS['warning_hover']});
        color: white;
    }}

    QPushButton.warning:hover {{
        background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                                   stop:0 {COLORS['warning_hover']},
                                   stop:1 {COLORS['warning']});
    }}
    """


def get_combobox_style() -> str:
    """现代化下拉框样式"""
    return f"""
    QComboBox {{
        background-color: {COLORS['surface']};
        border: 2px solid {COLORS['border']};
        border-radius: 10px;
        padding: 10px 16px;
        min-width: 140px;
        color: {COLORS['text_primary']};
        font-size: 14px;
        font-weight: 500;
    }}

    QComboBox:hover {{
        border-color: {COLORS['primary']};
        background-color: {COLORS['card']};
    }}

    QComboBox:focus {{
        border-color: {COLORS['primary']};
        outline: none;
    }}

    QComboBox::drop-down {{
        border: none;
        width: 30px;
        background: transparent;
    }}

    QComboBox::down-arrow {{
        image: none;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 6px solid {COLORS['text_secondary']};
        margin-right: 8px;
    }}

    QComboBox QAbstractItemView {{
        background-color: {COLORS['surface']};
        border: 2px solid {COLORS['border']};
        border-radius: 10px;
        selection-background-color: {COLORS['primary']};
        selection-color: white;
        padding: 8px;
        font-size: 14px;
    }}

    QComboBox QAbstractItemView::item {{
        padding: 8px 16px;
        border-radius: 6px;
        margin: 2px;
    }}

    QComboBox QAbstractItemView::item:hover {{
        background-color: {COLORS['card']};
    }}
    """


def get_label_style() -> str:
    """标签样式"""
    return f"""
    QLabel {{
        color: {COLORS['text_primary']};
        background-color: transparent;
    }}
    
    QLabel.title {{
        font-size: 24px;
        font-weight: bold;
        color: {COLORS['primary']};
    }}
    
    QLabel.subtitle {{
        font-size: 16px;
        font-weight: bold;
        color: {COLORS['text_primary']};
    }}
    
    QLabel.secondary {{
        color: {COLORS['text_secondary']};
    }}
    
    QLabel.link {{
        color: {COLORS['primary']};
        text-decoration: underline;
    }}
    
    QLabel.link:hover {{
        color: {COLORS['primary_hover']};
    }}
    
    QLabel.warning {{
        color: {COLORS['warning_text']};
        background-color: {COLORS['warning_bg']};
        padding: 12px;
        border-radius: 6px;
    }}
    """


def get_textedit_style() -> str:
    """现代化文本编辑器样式"""
    return f"""
    QTextEdit {{
        background-color: {COLORS['surface']};
        border: 2px solid {COLORS['border']};
        border-radius: 12px;
        padding: 16px;
        color: {COLORS['text_primary']};
        font-family: 'SF Mono', 'Monaco', 'Consolas', monospace;
        font-size: 13px;
        line-height: 1.5;
    }}

    QTextEdit:focus {{
        border-color: {COLORS['primary']};
        outline: none;
    }}
    """


def get_scrollarea_style() -> str:
    """滚动区域样式"""
    return f"""
    QScrollArea {{
        background-color: {COLORS['background']};
        border: none;
    }}
    
    QScrollBar:vertical {{
        background-color: {COLORS['background']};
        width: 12px;
        border-radius: 6px;
    }}
    
    QScrollBar::handle:vertical {{
        background-color: {COLORS['text_disabled']};
        border-radius: 6px;
        min-height: 20px;
    }}
    
    QScrollBar::handle:vertical:hover {{
        background-color: {COLORS['text_secondary']};
    }}
    
    QScrollBar::add-line:vertical,
    QScrollBar::sub-line:vertical {{
        height: 0px;
    }}
    """


def get_card_style() -> str:
    """卡片样式"""
    return f"""
    QFrame.card {{
        background-color: {COLORS['surface']};
        border: 1px solid {COLORS['border']};
        border-radius: 16px;
        padding: 20px;
        margin: 8px;
    }}

    QFrame.card:hover {{
        border-color: {COLORS['border_hover']};
    }}
    """


def get_complete_style() -> str:
    """获取完整的应用样式"""
    return (
        get_main_window_style() +
        get_button_style() +
        get_combobox_style() +
        get_label_style() +
        get_textedit_style() +
        get_scrollarea_style() +
        get_card_style()
    )
