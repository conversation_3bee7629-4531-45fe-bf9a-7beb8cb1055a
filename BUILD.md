# 构建说明

本项目支持多种方式构建可执行文件，包括GitHub Actions自动构建和本地手动构建。

## 🚀 GitHub Actions 自动构建

### 触发条件
- 推送到 `main` 或 `master` 分支
- 创建以 `v` 开头的标签（如 `v1.0.0`）
- 手动触发（在GitHub仓库的Actions页面）

### 构建产物
自动构建会生成以下平台的可执行文件：
- **Linux**: `AugmentCode-Free-linux.tar.gz`
- **Windows**: `AugmentCode-Free-windows.zip`
- **macOS**: `AugmentCode-Free-macos.tar.gz`

### 发布流程
1. 创建标签并推送：
   ```bash
   git tag v1.0.0
   git push origin v1.0.0
   ```
2. GitHub Actions会自动构建所有平台的可执行文件
3. 构建完成后会自动创建GitHub Release并上传文件

## 🔨 本地构建

### 前置要求
- Python 3.8+
- pip
- Git

### Linux/macOS 构建
```bash
# 克隆仓库
git clone <your-repo-url>
cd AugmentCode-Free

# 使用构建脚本
./build.sh

# 或者使用虚拟环境构建
./build.sh --venv
```

### Windows 构建
```cmd
# 克隆仓库
git clone <your-repo-url>
cd AugmentCode-Free

# 使用构建脚本
build.bat
```

### 手动构建
```bash
# 安装依赖
pip install -r requirements.txt
pip install pyinstaller

# 构建（Linux/macOS）
pyinstaller --onefile --windowed \
    --name "AugmentCode-Free" \
    --add-data "config:config" \
    --add-data "core:core" \
    --add-data "gui_qt6:gui_qt6" \
    --add-data "locales:locales" \
    --add-data "utils:utils" \
    --hidden-import PyQt6 \
    --hidden-import PyQt6.QtCore \
    --hidden-import PyQt6.QtWidgets \
    --hidden-import PyQt6.QtGui \
    --hidden-import psutil \
    main.py

# 构建（Windows）
pyinstaller --onefile --windowed ^
    --name "AugmentCode-Free" ^
    --add-data "config;config" ^
    --add-data "core;core" ^
    --add-data "gui_qt6;gui_qt6" ^
    --add-data "locales;locales" ^
    --add-data "utils;utils" ^
    --hidden-import PyQt6 ^
    --hidden-import PyQt6.QtCore ^
    --hidden-import PyQt6.QtWidgets ^
    --hidden-import PyQt6.QtGui ^
    --hidden-import psutil ^
    main.py
```

## 📁 文件结构

构建完成后的文件结构：
```
dist/
├── AugmentCode-Free-linux          # Linux可执行文件
├── AugmentCode-Free-linux.tar.gz   # Linux压缩包
├── AugmentCode-Free-windows.exe    # Windows可执行文件
├── AugmentCode-Free-windows.zip    # Windows压缩包
├── AugmentCode-Free-macos          # macOS可执行文件
└── AugmentCode-Free-macos.tar.gz   # macOS压缩包
```

## 🐛 常见问题

### Linux系统缺少Qt依赖
如果在Linux上运行时遇到Qt平台插件错误，请安装系统依赖：

**Ubuntu/Debian:**
```bash
sudo apt install libxcb-xinerama0 libxcb-cursor0 libxcb-icccm4 libxcb-image0 libxcb-keysyms1 libxcb-render-util0 libxcb-shape0 libxcb-util1 libxcb-xkb1 libxkbcommon-x11-0
```

**CentOS/RHEL/Fedora:**
```bash
sudo yum install libxcb-devel xcb-util-devel xcb-util-cursor-devel xcb-util-keysyms-devel xcb-util-image-devel xcb-util-wm-devel xcb-util-renderutil-devel
```

**Arch Linux:**
```bash
sudo pacman -S libxcb xcb-util xcb-util-cursor xcb-util-keysyms xcb-util-image xcb-util-wm xcb-util-renderutil
```

### Windows Defender误报
Windows Defender可能会将PyInstaller生成的可执行文件标记为病毒，这是误报。可以：
1. 将文件添加到Windows Defender排除列表
2. 使用代码签名证书对可执行文件进行签名

### macOS权限问题
在macOS上首次运行时可能需要：
1. 右键点击可执行文件，选择"打开"
2. 在系统偏好设置中允许运行来自未知开发者的应用

## 📝 注意事项

1. 构建的可执行文件较大（约50-100MB），这是正常的，因为包含了Python运行时和所有依赖
2. 首次启动可能较慢，这是PyInstaller的特性
3. 建议在目标平台上测试构建的可执行文件
4. 如果需要减小文件大小，可以考虑使用`--exclude-module`排除不需要的模块
