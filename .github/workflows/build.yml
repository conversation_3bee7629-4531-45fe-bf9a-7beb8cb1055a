name: Build Desktop Applications

on:
  push:
    branches: [ main, master, test ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, master, test ]
  workflow_dispatch:

jobs:
  build:
    strategy:
      matrix:
        include:
          - os: windows-latest
            platform: windows
            executable_suffix: .exe
            archive_format: zip
            runner_arch: x64
          - os: macos-13
            platform: macos-intel
            executable_suffix: ""
            archive_format: tar.gz
            runner_arch: x64
          - os: macos-14
            platform: macos-apple
            executable_suffix: ""
            archive_format: tar.gz
            runner_arch: arm64

    runs-on: ${{ matrix.os }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller

    - name: Build Windows executable
      if: matrix.platform == 'windows'
      run: |
        pyinstaller --onefile ^
          --windowed ^
          --name "AugmentCode-Free-Windows-x64" ^
          --add-data "config;config" ^
          --add-data "core;core" ^
          --add-data "gui_qt6;gui_qt6" ^
          --add-data "locales;locales" ^
          --add-data "utils;utils" ^
          --hidden-import PyQt6 ^
          --hidden-import PyQt6.QtCore ^
          --hidden-import PyQt6.QtWidgets ^
          --hidden-import PyQt6.QtGui ^
          --hidden-import psutil ^
          --hidden-import click ^
          --hidden-import colorama ^
          main.py

    - name: Build macOS executable (Intel)
      if: matrix.platform == 'macos-intel'
      run: |
        pyinstaller --onefile \
          --windowed \
          --name "AugmentCode-Free-macOS-Intel" \
          --add-data "config:config" \
          --add-data "core:core" \
          --add-data "gui_qt6:gui_qt6" \
          --add-data "locales:locales" \
          --add-data "utils:utils" \
          --hidden-import PyQt6 \
          --hidden-import PyQt6.QtCore \
          --hidden-import PyQt6.QtWidgets \
          --hidden-import PyQt6.QtGui \
          --hidden-import psutil \
          --hidden-import click \
          --hidden-import colorama \
          --target-arch x86_64 \
          main.py

    - name: Build macOS executable (Apple Silicon)
      if: matrix.platform == 'macos-apple'
      run: |
        pyinstaller --onefile \
          --windowed \
          --name "AugmentCode-Free-macOS-AppleSilicon" \
          --add-data "config:config" \
          --add-data "core:core" \
          --add-data "gui_qt6:gui_qt6" \
          --add-data "locales:locales" \
          --add-data "utils:utils" \
          --hidden-import PyQt6 \
          --hidden-import PyQt6.QtCore \
          --hidden-import PyQt6.QtWidgets \
          --hidden-import PyQt6.QtGui \
          --hidden-import psutil \
          --hidden-import click \
          --hidden-import colorama \
          --target-arch arm64 \
          main.py

    - name: Create Windows installer
      if: matrix.platform == 'windows'
      run: |
        cd dist
        7z a "AugmentCode-Free-Windows-x64.zip" "AugmentCode-Free-Windows-x64.exe"

    - name: Create macOS app bundle (Intel)
      if: matrix.platform == 'macos-intel'
      run: |
        cd dist
        # 创建.app包结构
        mkdir -p "AugmentCode-Free.app/Contents/MacOS"
        mkdir -p "AugmentCode-Free.app/Contents/Resources"

        # 移动可执行文件
        mv "AugmentCode-Free-macOS-Intel" "AugmentCode-Free.app/Contents/MacOS/AugmentCode-Free"
        chmod +x "AugmentCode-Free.app/Contents/MacOS/AugmentCode-Free"

        # 创建Info.plist
        cat > "AugmentCode-Free.app/Contents/Info.plist" << 'EOF'
        <?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
        <plist version="1.0">
        <dict>
            <key>CFBundleExecutable</key>
            <string>AugmentCode-Free</string>
            <key>CFBundleIdentifier</key>
            <string>com.augmentcode.free</string>
            <key>CFBundleName</key>
            <string>AugmentCode-Free</string>
            <key>CFBundleVersion</key>
            <string>1.0.0</string>
            <key>CFBundleShortVersionString</key>
            <string>1.0.0</string>
            <key>CFBundlePackageType</key>
            <string>APPL</string>
            <key>LSMinimumSystemVersion</key>
            <string>10.14</string>
            <key>LSArchitecturePriority</key>
            <array>
                <string>x86_64</string>
            </array>
        </dict>
        </plist>
        EOF

        # 创建压缩包
        tar -czf "AugmentCode-Free-macOS-Intel.tar.gz" "AugmentCode-Free.app"

    - name: Create macOS app bundle (Apple Silicon)
      if: matrix.platform == 'macos-apple'
      run: |
        cd dist
        # 创建.app包结构
        mkdir -p "AugmentCode-Free.app/Contents/MacOS"
        mkdir -p "AugmentCode-Free.app/Contents/Resources"

        # 移动可执行文件
        mv "AugmentCode-Free-macOS-AppleSilicon" "AugmentCode-Free.app/Contents/MacOS/AugmentCode-Free"
        chmod +x "AugmentCode-Free.app/Contents/MacOS/AugmentCode-Free"

        # 创建Info.plist
        cat > "AugmentCode-Free.app/Contents/Info.plist" << 'EOF'
        <?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
        <plist version="1.0">
        <dict>
            <key>CFBundleExecutable</key>
            <string>AugmentCode-Free</string>
            <key>CFBundleIdentifier</key>
            <string>com.augmentcode.free</string>
            <key>CFBundleName</key>
            <string>AugmentCode-Free</string>
            <key>CFBundleVersion</key>
            <string>1.0.0</string>
            <key>CFBundleShortVersionString</key>
            <string>1.0.0</string>
            <key>CFBundlePackageType</key>
            <string>APPL</string>
            <key>LSMinimumSystemVersion</key>
            <string>11.0</string>
            <key>LSArchitecturePriority</key>
            <array>
                <string>arm64</string>
            </array>
        </dict>
        </plist>
        EOF

        # 创建压缩包
        tar -czf "AugmentCode-Free-macOS-AppleSilicon.tar.gz" "AugmentCode-Free.app"

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: AugmentCode-Free-${{ matrix.platform }}
        path: |
          dist/*.zip
          dist/*.tar.gz

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')

    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v3

    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          AugmentCode-Free-windows/AugmentCode-Free-Windows-x64.zip
          AugmentCode-Free-macos-intel/AugmentCode-Free-macOS-Intel.tar.gz
          AugmentCode-Free-macos-apple/AugmentCode-Free-macOS-AppleSilicon.tar.gz
        draft: false
        prerelease: false
        body: |
          ## 🚀 AugmentCode-Free Release

          ### 📦 下载说明
          - **Windows (x64)**: `AugmentCode-Free-Windows-x64.zip` - 解压后双击 `.exe` 文件运行
          - **macOS (Intel)**: `AugmentCode-Free-macOS-Intel.tar.gz` - 解压后双击 `.app` 文件运行
          - **macOS (Apple Silicon)**: `AugmentCode-Free-macOS-AppleSilicon.tar.gz` - 解压后双击 `.app` 文件运行

          ### ⚠️ 注意事项
          - **Windows**: 首次运行可能被Windows Defender拦截，请添加信任
          - **macOS**: 首次运行需要右键点击选择"打开"，或在系统偏好设置中允许运行

          ### 🔧 系统要求
          - **Windows**: Windows 10 或更高版本
          - **macOS**: macOS 10.14 (Intel) / macOS 11.0 (Apple Silicon) 或更高版本
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
