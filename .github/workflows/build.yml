name: Build Executables

on:
  push:
    branches: [ main, master ]
    tags: [ 'v*' ]
  pull_request:
    branches: [ main, master ]
  workflow_dispatch:

jobs:
  build:
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
          - os: ubuntu-latest
            platform: linux
            executable_suffix: ""
            archive_format: tar.gz
          - os: windows-latest
            platform: windows
            executable_suffix: .exe
            archive_format: zip
          - os: macos-latest
            platform: macos
            executable_suffix: ""
            archive_format: tar.gz

    runs-on: ${{ matrix.os }}

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.11'

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pyinstaller

    - name: Build executable (Linux/macOS)
      if: matrix.os != 'windows-latest'
      run: |
        pyinstaller --onefile \
          --windowed \
          --name "AugmentCode-Free-${{ matrix.platform }}" \
          --add-data "config:config" \
          --add-data "core:core" \
          --add-data "gui_qt6:gui_qt6" \
          --add-data "locales:locales" \
          --add-data "utils:utils" \
          --hidden-import PyQt6 \
          --hidden-import PyQt6.QtCore \
          --hidden-import PyQt6.QtWidgets \
          --hidden-import PyQt6.QtGui \
          --hidden-import psutil \
          main.py

    - name: Build executable (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        pyinstaller --onefile ^
          --windowed ^
          --name "AugmentCode-Free-${{ matrix.platform }}" ^
          --add-data "config;config" ^
          --add-data "core;core" ^
          --add-data "gui_qt6;gui_qt6" ^
          --add-data "locales;locales" ^
          --add-data "utils;utils" ^
          --hidden-import PyQt6 ^
          --hidden-import PyQt6.QtCore ^
          --hidden-import PyQt6.QtWidgets ^
          --hidden-import PyQt6.QtGui ^
          --hidden-import psutil ^
          main.py

    - name: Create archive (Linux/macOS)
      if: matrix.os != 'windows-latest'
      run: |
        cd dist
        tar -czf "AugmentCode-Free-${{ matrix.platform }}.tar.gz" "AugmentCode-Free-${{ matrix.platform }}"

    - name: Create archive (Windows)
      if: matrix.os == 'windows-latest'
      run: |
        cd dist
        7z a "AugmentCode-Free-${{ matrix.platform }}.zip" "AugmentCode-Free-${{ matrix.platform }}.exe"

    - name: Upload artifacts
      uses: actions/upload-artifact@v3
      with:
        name: AugmentCode-Free-${{ matrix.platform }}
        path: |
          dist/AugmentCode-Free-${{ matrix.platform }}.${{ matrix.archive_format }}

  release:
    needs: build
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/v')
    
    steps:
    - name: Download all artifacts
      uses: actions/download-artifact@v3
      
    - name: Create Release
      uses: softprops/action-gh-release@v1
      with:
        files: |
          AugmentCode-Free-linux/AugmentCode-Free-linux.tar.gz
          AugmentCode-Free-windows/AugmentCode-Free-windows.zip
          AugmentCode-Free-macos/AugmentCode-Free-macos.tar.gz
        draft: false
        prerelease: false
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
